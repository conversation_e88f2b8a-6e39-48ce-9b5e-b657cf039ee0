.stepper-lbl {
  color: #1f4a70;
}

.stepper-card-unchecked-crl {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #CCCCCC;
}

.fs-16 {
  font-size: 16px;
}

.fw-600 {
  font-weight: 600;
}

.fs-14 {
  font-size: 14px;
}

.fw-500 {
  font-weight: 500;
}

.secondary-color {
  color: #6c757d;
}

.primary-txt-color {
  color: #1f4a70;
}

.stepper-card {
  border: 1px solid #CCCCCC !important;
  box-shadow: none !important;
  border-radius: 6px;
  padding: 16px 12px;
  gap: 8px;
  cursor: default;
  opacity: 0.6 !important;
}

.stepper-card-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stepper-card-active {
  border: 2px solid #A6CBF3 !important;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2) !important;
  opacity: 1 !important;

}

.stepper-card-completed {
  opacity: 1 !important;
  cursor: pointer !important;
}

.stepper-card-lbl {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #1f4a70 !important;
}

.stepper-card-step {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #6c757d !important;
}

.stepper-card-unchecked-crl {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #CCCCCC;
}

.stepper-card-step {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #6c757d !important;
}

.stepper-card-row {
  align-items: center;
  justify-content: space-between;
}

.stepper-bottom-container {
  height: 58px;
  background-color: #f8f9fa;
  border-top: 1px solid #cccccc;
  text-align: center;
  padding-top: 10px;
}

.stepper-bottom-button {
  padding-left: 100px;
  padding-right: 100px;
  margin-right: 8px;
  &:last-child {
    margin-right: 0;
  }
}