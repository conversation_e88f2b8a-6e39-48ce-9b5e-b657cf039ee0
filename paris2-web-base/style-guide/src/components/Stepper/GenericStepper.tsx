import React, {useState} from 'react';
import {Card, Col, Container, Row} from 'react-bootstrap';
import Stepper from './Stepper';
import BottomButton, {ButtonConfig} from './BottomButton';
import { CloseIcon } from './svgIcons';

export interface StepConfig {
  label: string;
  component: React.ReactNode;
}

interface GenericStepperProps {
  steps: StepConfig[];
  onNext?: (currentStep: number) => Promise<void> | void;
  breadCrumbTitle?: string;
  additionalButtons?: ButtonConfig[];
  primaryBtnTitle:
    | string
    | ((currentStep: number, totalSteps: number) => string);
  secondaryBtnTitle?: string;
  secondaryBtnOnClick?: (currentStep: number) => void | Promise<void>;
  primaryBtnOnClick: () => Promise<void> | void;
  primaryBtnDisabled?: boolean;
  secondaryBtnDisabled?: boolean;
  onClose?: () => void;
  isPreview?: boolean;
  previewComponent?: React.ReactNode;
  onStepChange?: (currentStep: number) => void;
  defaultLoadStep?: number;
}

const GenericStepper: React.FC<GenericStepperProps> = ({
  steps,
  onNext,
  breadCrumbTitle,
  additionalButtons = [],
  primaryBtnTitle = 'Next',
  secondaryBtnTitle = 'Cancel',
  primaryBtnDisabled = false,
  secondaryBtnDisabled = false,
  secondaryBtnOnClick,
  primaryBtnOnClick,
  onClose,
  isPreview = false,
  previewComponent,
  onStepChange,
  defaultLoadStep,
}) => {
  const [step, setStep] = useState(defaultLoadStep ?? 1);

  const handleStepsChange = async (newStep: number) => {
    setStep(newStep);
    if (onStepChange) await onStepChange(newStep);
  };

  const handleNext = async () => {
    if (step < steps.length) {
      if (onNext) await onNext(step);
      setStep(step + 1);
    } else if (step === steps.length) {
      if (primaryBtnOnClick) await primaryBtnOnClick();
    }
  };

  return (
    <>
      {breadCrumbTitle && (
        <div
          className="fs-24 priimary-color"
          style={{
            marginLeft: 15,
            marginRight: 15,
            color: '#1F4A70',
            justifyContent: 'space-between',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <span className="fs-24 priimary-color">{breadCrumbTitle}</span>
          <button
            type="button"
            aria-label="Close"
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              padding: 0,
              marginLeft: 8,
              cursor: 'pointer',
              verticalAlign: 'middle',
            }}
          >
            <CloseIcon />
          </button>
        </div>
      )}
      <Container fluid className="mt-4">
        <Row>
          {!isPreview && (
            <Col md={4} lg={3} className="pe-3">
              <Stepper
                steps={steps.map(s => s.label)}
                currentStep={step}
                setStep={handleStepsChange}
              />
            </Col>
          )}
          <Col md={isPreview ? 12 : 8} lg={isPreview ? 12 : 9}>
            <Card
              className={`p-3 border border-2 border-dashed rounded-6 prj-form-card`}
              style={{
                height: 'calc(100vh - 225px)',
                overflowY: 'auto',
                overflowX: 'hidden',
              }}
            >
              {isPreview ? previewComponent : steps[step - 1]?.component}
            </Card>
          </Col>
        </Row>
      </Container>
      <BottomButton
        buttons={[
          ...additionalButtons,
          {
            title: secondaryBtnTitle,
            testID: 'form-prj-cancel-btn',
            variant: 'secondary',
            customClass: 'sec-btn fs-14',
            onClick: secondaryBtnOnClick
              ? () => secondaryBtnOnClick(step)
              : undefined,
            disabled: secondaryBtnDisabled,
          },
          {
            title:
              typeof primaryBtnTitle === 'function'
                ? primaryBtnTitle(step, steps.length)
                : primaryBtnTitle,
            testID: 'form-prj-save-btn',
            variant: 'primary',
            customClass: 'primary-btn fs-14 btn-secondary-bg',
            onClick: () => {
              // eslint-disable-next-line no-void
              void handleNext();
            },
            disabled: primaryBtnDisabled,
          },
        ]}
      />
    </>
  );
};

export default GenericStepper;
