import React, {useState, useRef, useEffect} from 'react';
import {Card, Col, Container, Row} from 'react-bootstrap';
import Stepper from './Stepper';
import BottomButton, {ButtonConfig} from './BottomButton';
import { CloseIcon } from './svgIcons';

export interface StepConfig {
  label: string;
  component: React.ReactNode;
  validate?: () => boolean | Promise<boolean>;
}

interface TestIds {
  closeBtn?: string;
  stepper?: string;
  card?: string;
  secondaryBtn?: string;
  primaryBtn?: string;
}

interface GenericStepperProps {
  steps: StepConfig[];
  onNext?: (currentStep: number) => Promise<void> | void;
  breadCrumbTitle?: string;
  additionalButtons?: ButtonConfig[];
  primaryBtnTitle:
    | string
    | ((currentStep: number, totalSteps: number) => string);
  secondaryBtnTitle?: string;
  secondaryBtnOnClick?: (currentStep: number) => void | Promise<void>;
  primaryBtnOnClick: () => Promise<void> | void;
  primaryBtnDisabled?: boolean;
  secondaryBtnDisabled?: boolean;
  onClose?: () => void;
  isPreview?: boolean;
  previewComponent?: React.ReactNode;
  onStepChange?: (currentStep: number, previousStep: number) => void;
  defaultLoadStep?: number;
  customStepper?: React.ReactNode;
  testIds?: TestIds;
}

const GenericStepper: React.FC<GenericStepperProps> = ({
  steps,
  onNext,
  breadCrumbTitle,
  additionalButtons = [],
  primaryBtnTitle = 'Next',
  secondaryBtnTitle = 'Cancel',
  primaryBtnDisabled = false,
  secondaryBtnDisabled = false,
  secondaryBtnOnClick,
  primaryBtnOnClick,
  onClose,
  isPreview = false,
  previewComponent,
  onStepChange,
  defaultLoadStep,
  customStepper,
  testIds = {},
}) => {
  const [step, setStep] = useState(defaultLoadStep ?? 1);
  const [loading, setLoading] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (cardRef.current) {
      cardRef.current.scrollTo({top: 0, behavior: 'smooth'});
    }
  }, [step, isPreview]);

  const handleStepsChange = async (newStep: number) => {
    const prevStep = step;
    setStep(newStep);
    if (onStepChange) await onStepChange(newStep, prevStep);
  };

  const handleNext = async () => {
    setLoading(true);
    const currentStepConfig = steps[step - 1];
    if (currentStepConfig?.validate) {
      const isValid = await currentStepConfig.validate();
      if (!isValid) {
        setLoading(false);
        throw new Error('Validation failed. Please complete the required fields.');
      }
    }
    if (step < steps.length) {
      if (onNext) await onNext(step);
      setStep(step + 1);
    } else if (step === steps.length) {
      if (primaryBtnOnClick) await primaryBtnOnClick();
    }
    setLoading(false);
  };

  return (
    <>
      {breadCrumbTitle && (
        <div className="stepper-breadcrumb-container">
          <span className="stepper-breadcrumb-title">{breadCrumbTitle}</span>
          <button
            type="button"
            aria-label="Close"
            onClick={onClose}
            className="stepper-close-btn"
            data-testid={testIds.closeBtn}
          >
            <CloseIcon />
          </button>
        </div>
      )}
      <Container fluid className="mt-4">
        <Row>
          {!isPreview && (
            <Col md={4} lg={3} className="pe-3">
              {customStepper ? (
                customStepper
              ) : (
                <Stepper
                  steps={steps.map((s) => s.label)}
                  currentStep={step}
                  setStep={handleStepsChange}
                  data-testid={testIds.stepper}
                />
              )}
            </Col>
          )}
          <Col md={isPreview ? 12 : 8} lg={isPreview ? 12 : 9}>
            <Card
              ref={cardRef}
              className="stepper-card-main prj-form-card"
              data-testid={testIds.card}
            >
              {isPreview ? previewComponent : steps[step - 1]?.component}
            </Card>
          </Col>
        </Row>
      </Container>
      <BottomButton
        buttons={[
          ...additionalButtons,
          {
            title: secondaryBtnTitle,
            testID: testIds.secondaryBtn ?? "form-prj-cancel-btn",
            variant: "secondary",
            customClass: "stepper-btm-sec-btn",
            onClick: secondaryBtnOnClick
              ? () => secondaryBtnOnClick(step)
              : undefined,
            disabled: secondaryBtnDisabled,
          },
          {
            title:
              typeof primaryBtnTitle === "function"
                ? primaryBtnTitle(step, steps.length)
                : primaryBtnTitle,
            testID: testIds.primaryBtn ?? "form-prj-save-btn",
            variant: "primary",
            customClass: "primary-btn fs-14 btn-secondary-bg",
            onClick: () => {
              // eslint-disable-next-line no-void
              void handleNext();
            },
            disabled: primaryBtnDisabled,
          },
        ]}
      />
    </>
  );
};

export default GenericStepper;
