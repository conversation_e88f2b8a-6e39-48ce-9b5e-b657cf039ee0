import React, {useState, useRef, useEffect} from 'react';
import {Card, Col, Container, Row} from 'react-bootstrap';
import Stepper from './Stepper';
import BottomButton, {ButtonConfig} from './BottomButton';
import { CloseIcon } from './svgIcons';

export interface StepConfig {
  label: string;
  component: React.ReactNode;
  validate?: () => boolean | Promise<boolean>;
}

interface GenericStepperProps {
  steps: StepConfig[];
  onNext?: (currentStep: number) => Promise<void> | void;
  breadCrumbTitle?: string;
  additionalButtons?: ButtonConfig[];
  primaryBtnTitle:
    | string
    | ((currentStep: number, totalSteps: number) => string);
  secondaryBtnTitle?: string;
  secondaryBtnOnClick?: (currentStep: number) => void | Promise<void>;
  primaryBtnOnClick: () => Promise<void> | void;
  primaryBtnDisabled?: boolean;
  secondaryBtnDisabled?: boolean;
  onClose?: () => void;
  isPreview?: boolean;
  previewComponent?: React.ReactNode;
  onStepChange?: (currentStep: number) => void;
  defaultLoadStep?: number;
  customStepper?: React.ReactNode;
}

const GenericStepper: React.FC<GenericStepperProps> = ({
  steps,
  onNext,
  breadCrumbTitle,
  additionalButtons = [],
  primaryBtnTitle = 'Next',
  secondaryBtnTitle = 'Cancel',
  primaryBtnDisabled = false,
  secondaryBtnDisabled = false,
  secondaryBtnOnClick,
  primaryBtnOnClick,
  onClose,
  isPreview = false,
  previewComponent,
  onStepChange,
  defaultLoadStep,
  customStepper,
}) => {
  const [step, setStep] = useState(defaultLoadStep ?? 1);
  const [loading, setLoading] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (cardRef.current) {
      cardRef.current.scrollTo({top: 0, behavior: 'smooth'});
    }
  }, [step, isPreview]);

  const handleStepsChange = async (newStep: number) => {
    setStep(newStep);
    if (onStepChange) await onStepChange(newStep);
  };

  const handleNext = async () => {
    setLoading(true);
    try {
      const currentStepConfig = steps[step - 1];
      if (currentStepConfig?.validate) {
        const isValid = await currentStepConfig.validate();
        if (!isValid) return;
      }
      if (step < steps.length) {
        if (onNext) await onNext(step);
        setStep(step + 1);
      } else if (step === steps.length) {
        if (primaryBtnOnClick) await primaryBtnOnClick();
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {breadCrumbTitle && (
        <div className="stepper-breadcrumb-container">
          <span className="stepper-breadcrumb-title">{breadCrumbTitle}</span>
          <button
            type="button"
            aria-label="Close"
            onClick={onClose}
            className="stepper-close-btn"
          >
            <CloseIcon />
          </button>
        </div>
      )}
      <Container fluid className="mt-4">
        <Row>
          {!isPreview && (
            <Col md={4} lg={3} className="pe-3">
              {customStepper ? (
                customStepper
              ) : (
                <Stepper
                  steps={steps.map((s) => s.label)}
                  currentStep={step}
                  setStep={handleStepsChange}
                />
              )}
            </Col>
          )}
          <Col md={isPreview ? 12 : 8} lg={isPreview ? 12 : 9}>
            <Card ref={cardRef} className="stepper-card-main prj-form-card">
              {isPreview ? previewComponent : steps[step - 1]?.component}
            </Card>
          </Col>
        </Row>
      </Container>
      <BottomButton
        buttons={[
          ...additionalButtons,
          {
            title: secondaryBtnTitle,
            testID: "form-prj-cancel-btn",
            variant: "secondary",
            customClass: "stepper-btm-sec-btn",
            onClick: secondaryBtnOnClick
              ? () => secondaryBtnOnClick(step)
              : undefined,
            disabled: secondaryBtnDisabled,
          },
          {
            title:
              typeof primaryBtnTitle === "function"
                ? primaryBtnTitle(step, steps.length)
                : primaryBtnTitle,
            testID: "form-prj-save-btn",
            variant: "primary",
            customClass: "primary-btn fs-14 btn-secondary-bg",
            onClick: () => {
              // eslint-disable-next-line no-void
              void handleNext();
            },
            disabled: primaryBtnDisabled,
          },
        ]}
      />
    </>
  );
};

export default GenericStepper;
